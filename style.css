/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
}

/* حاوية الهيرو */
.hero-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1000px;
    overflow: hidden;
}

/* الخلفية المتحركة */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
        radial-gradient(2px 2px at 40px 70px, #ff00ff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ffff00, transparent),
        radial-gradient(1px 1px at 130px 80px, #00ff00, transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: particlesFloat 20s linear infinite;
    opacity: 0.3;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 15s linear infinite;
}

@keyframes particlesFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* محتوى الهيرو */
.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1400px;
    width: 100%;
    padding: 2rem;
    align-items: center;
}

/* قسم النص */
.text-section {
    color: white;
    animation: slideInRight 1s ease-out;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    font-weight: 300;
    color: #00ffff;
    margin-bottom: 0.5rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite, fadeInUp 1s ease-out 0.4s both;
    margin-bottom: 0.5rem;
}

.title-subtitle {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    color: #cccccc;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #b0b0b0;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    animation: fadeInUp 1s ease-out 1s both;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.btn-secondary:hover {
    background: #00ffff;
    color: #0a0a0a;
    transform: translateY(-3px);
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* حاوية الروبوت */
.robot-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
    animation: slideInLeft 1s ease-out;
}

.robot {
    position: relative;
    transform-style: preserve-3d;
    animation: robotFloat 4s ease-in-out infinite;
    transition: transform 0.3s ease;
}

@keyframes robotFloat {
    0%, 100% { transform: translateY(0px) rotateY(0deg); }
    50% { transform: translateY(-20px) rotateY(5deg); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* رأس الروبوت */
.robot-head {
    position: relative;
    z-index: 10;
}

.head-main {
    width: 120px;
    height: 100px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 20px 20px 15px 15px;
    position: relative;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.5),
        inset 0 2px 10px rgba(255, 255, 255, 0.1);
    transform: translateZ(50px);
    animation: headBob 3s ease-in-out infinite;
}

@keyframes headBob {
    0%, 100% { transform: translateZ(50px) rotateX(0deg); }
    50% { transform: translateZ(50px) rotateX(5deg); }
}

/* العيون */
.eyes {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
}

.eye {
    width: 25px;
    height: 25px;
    background: #000;
    border-radius: 50%;
    position: relative;
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.5),
        inset 0 0 10px rgba(0, 255, 255, 0.3);
    animation: eyeBlink 4s ease-in-out infinite;
}

.pupil {
    width: 15px;
    height: 15px;
    background: #00ffff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 15px #00ffff;
    animation: pupilMove 6s ease-in-out infinite;
}

.eye-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
    animation: glowPulse 2s ease-in-out infinite;
}

@keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes pupilMove {
    0%, 100% { transform: translate(-50%, -50%); }
    25% { transform: translate(-40%, -50%); }
    50% { transform: translate(-50%, -40%); }
    75% { transform: translate(-60%, -50%); }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* الفم */
.mouth {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 8px;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
}

.mouth-line {
    width: 100%;
    height: 2px;
    background: #00ffff;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    animation: mouthTalk 2s ease-in-out infinite;
}

@keyframes mouthTalk {
    0%, 100% { width: 20%; left: 40%; }
    50% { width: 80%; left: 10%; }
}

/* الهوائي */
.antenna {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
}

.antenna-base {
    width: 6px;
    height: 15px;
    background: linear-gradient(to top, #333, #555);
    border-radius: 3px;
    position: relative;
}

.antenna-tip {
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #ff00ff, #ff0080);
    border-radius: 50%;
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 20px #ff00ff;
    animation: antennaBlink 1.5s ease-in-out infinite;
}

@keyframes antennaBlink {
    0%, 100% { opacity: 1; transform: translateX(-50%) scale(1); }
    50% { opacity: 0.5; transform: translateX(-50%) scale(1.2); }
}

/* جسم الروبوت */
.robot-body {
    position: relative;
    margin-top: 10px;
    z-index: 5;
}

.body-main {
    width: 140px;
    height: 160px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 15px;
    position: relative;
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.6),
        inset 0 2px 15px rgba(255, 255, 255, 0.1);
    transform: translateZ(30px);
    animation: bodyPulse 4s ease-in-out infinite;
}

@keyframes bodyPulse {
    0%, 100% { transform: translateZ(30px) scale(1); }
    50% { transform: translateZ(30px) scale(1.02); }
}

/* لوحة الصدر */
.chest-panel {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 120px;
    background: linear-gradient(145deg, #1a1a2a, #0a0a1a);
    border-radius: 10px;
    border: 2px solid #333;
    padding: 10px;
}

/* زر الطاقة */
.power-button {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #00ff00, #008800);
    border-radius: 50%;
    margin: 0 auto 10px;
    box-shadow:
        0 0 20px rgba(0, 255, 0, 0.5),
        inset 0 2px 5px rgba(255, 255, 255, 0.3);
    animation: powerPulse 2s ease-in-out infinite;
    cursor: pointer;
}

@keyframes powerPulse {
    0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 0, 0.5), inset 0 2px 5px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: 0 0 30px rgba(0, 255, 0, 0.8), inset 0 2px 5px rgba(255, 255, 255, 0.3); }
}

/* أضواء الحالة */
.status-lights {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: lightBlink 3s ease-in-out infinite;
}

.light.red {
    background: radial-gradient(circle, #ff0000, #880000);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
    animation-delay: 0s;
}

.light.green {
    background: radial-gradient(circle, #00ff00, #008800);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
    animation-delay: 1s;
}

.light.blue {
    background: radial-gradient(circle, #0080ff, #004080);
    box-shadow: 0 0 10px rgba(0, 128, 255, 0.5);
    animation-delay: 2s;
}

@keyframes lightBlink {
    0%, 80%, 100% { opacity: 0.3; }
    10%, 70% { opacity: 1; }
}

/* الشاشة */
.screen {
    width: 100%;
    height: 40px;
    background: #000;
    border-radius: 5px;
    border: 1px solid #333;
    padding: 5px;
    position: relative;
    overflow: hidden;
}

.screen-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.data-line {
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    border-radius: 1px;
    animation: dataFlow 2s linear infinite;
}

.data-line:nth-child(2) {
    animation-delay: 0.5s;
}

.data-line:nth-child(3) {
    animation-delay: 1s;
}

@keyframes dataFlow {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

/* الأذرع */
.robot-arms {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 100px;
}

.arm {
    position: absolute;
    top: 0;
    transform-origin: top center;
}

.left-arm {
    left: -30px;
    animation: leftArmMove 5s ease-in-out infinite;
}

.right-arm {
    right: -30px;
    animation: rightArmMove 5s ease-in-out infinite;
}

@keyframes leftArmMove {
    0%, 100% { transform: rotate(-20deg); }
    50% { transform: rotate(-40deg); }
}

@keyframes rightArmMove {
    0%, 100% { transform: rotate(20deg); }
    50% { transform: rotate(40deg); }
}

.upper-arm {
    width: 20px;
    height: 50px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 10px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.lower-arm {
    width: 16px;
    height: 40px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 8px;
    position: absolute;
    top: 45px;
    left: 2px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    animation: lowerArmBend 3s ease-in-out infinite;
}

@keyframes lowerArmBend {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(15deg); }
}

.hand {
    width: 18px;
    height: 15px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 9px;
    position: absolute;
    top: 80px;
    left: 1px;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    padding: 2px;
}

.finger {
    width: 3px;
    height: 8px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 1.5px;
    animation: fingerMove 2s ease-in-out infinite;
}

.finger:nth-child(2) {
    animation-delay: 0.2s;
}

.finger:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fingerMove {
    0%, 100% { height: 8px; }
    50% { height: 6px; }
}

/* الأرجل */
.robot-legs {
    position: absolute;
    top: 280px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 120px;
}

.leg {
    position: absolute;
    top: 0;
    width: 25px;
    transform-origin: top center;
}

.left-leg {
    left: 15px;
    animation: leftLegMove 4s ease-in-out infinite;
}

.right-leg {
    right: 15px;
    animation: rightLegMove 4s ease-in-out infinite;
}

@keyframes leftLegMove {
    0%, 100% { transform: rotate(-5deg); }
    50% { transform: rotate(5deg); }
}

@keyframes rightLegMove {
    0%, 100% { transform: rotate(5deg); }
    50% { transform: rotate(-5deg); }
}

.upper-leg {
    width: 25px;
    height: 60px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 12px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.lower-leg {
    width: 20px;
    height: 50px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 10px;
    position: absolute;
    top: 55px;
    left: 2.5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.foot {
    width: 30px;
    height: 15px;
    background: linear-gradient(145deg, #2a2a3a, #1a1a2a);
    border-radius: 15px 15px 5px 5px;
    position: absolute;
    top: 100px;
    left: -2.5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

/* التأثيرات الإضافية */
.robot-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: energyField 8s linear infinite;
}

.energy-field::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    border: 1px solid rgba(255, 0, 255, 0.2);
    border-radius: 50%;
    animation: energyField 6s linear infinite reverse;
}

.energy-field::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 1px solid rgba(255, 255, 0, 0.1);
    border-radius: 50%;
    animation: energyField 4s linear infinite;
}

@keyframes energyField {
    0% { transform: translate(-50%, -50%) rotate(0deg); opacity: 0.3; }
    50% { opacity: 0.6; }
    100% { transform: translate(-50%, -50%) rotate(360deg); opacity: 0.3; }
}

.hologram-base {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 10px;
    background: radial-gradient(ellipse, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: hologramPulse 3s ease-in-out infinite;
}

@keyframes hologramPulse {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: translateX(-50%) scale(1.1);
        opacity: 0.8;
    }
}

/* مؤشرات التفاعل */
.interaction-hints {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.hint {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    font-size: 0.9rem;
    animation: hintFloat 3s ease-in-out infinite;
}

.hint-icon {
    font-size: 1.2rem;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes hintFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* التجاوب مع الشاشات المختلفة */
@media (max-width: 1200px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .robot-container {
        height: 500px;
    }

    .robot {
        transform: scale(0.8);
    }
}

@media (max-width: 768px) {
    .title-main {
        font-size: 3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
    }

    .robot {
        transform: scale(0.6);
    }

    .robot-container {
        height: 400px;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 1rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .robot {
        transform: scale(0.5);
    }

    .robot-container {
        height: 300px;
    }

    .interaction-hints {
        bottom: 10px;
    }

    .hint {
        font-size: 0.8rem;
        padding: 8px 15px;
    }
}
