/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: white;
}

/* شاشة التحميل */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 255, 255, 0.3);
    border-top: 4px solid #00ffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loading-text {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #00ffff;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #ff00ff);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

/* حاوية الهيرو */
.hero-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1000px;
    overflow: hidden;
}

/* الخلفية المتحركة */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
        radial-gradient(2px 2px at 40px 70px, #ff00ff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ffff00, transparent),
        radial-gradient(1px 1px at 130px 80px, #00ff00, transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: particlesFloat 20s linear infinite;
    opacity: 0.3;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 15s linear infinite;
}

@keyframes particlesFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* محتوى الهيرو */
.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1400px;
    width: 100%;
    padding: 2rem;
    align-items: center;
}

/* قسم النص */
.text-section {
    animation: slideInRight 1s ease-out;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    font-weight: 300;
    color: #00ffff;
    margin-bottom: 0.5rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite, fadeInUp 1s ease-out 0.4s both;
    margin-bottom: 0.5rem;
}

.title-subtitle {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    color: #cccccc;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #b0b0b0;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 1s both;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.btn-secondary:hover {
    background: #00ffff;
    color: #0a0a0a;
    transform: translateY(-3px);
}

/* لوحة التحكم */
.control-panel {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
    backdrop-filter: blur(10px);
    animation: fadeInUp 1s ease-out 1.2s both;
}

.control-panel h3 {
    color: #00ffff;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.9rem;
    color: #cccccc;
}

.control-group input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #00ffff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #00ffff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* مؤشرات الحالة */
.status-indicators {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.status-label {
    font-size: 0.8rem;
    color: #cccccc;
}

.status-value {
    font-size: 0.8rem;
    color: #00ffff;
    font-weight: 600;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* حاوية الروبوت */
.robot-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
    animation: slideInLeft 1s ease-out;
}

#robot-canvas {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    background: transparent;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التأثيرات الإضافية */
.robot-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 50%;
    animation: energyFieldRotate 12s linear infinite;
}

.energy-field::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 320px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: energyFieldRotate 8s linear infinite reverse;
}

.energy-field::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 240px;
    height: 240px;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: energyFieldRotate 6s linear infinite;
}

@keyframes energyFieldRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); opacity: 0.2; }
    50% { opacity: 0.5; }
    100% { transform: translate(-50%, -50%) rotate(360deg); opacity: 0.2; }
}

.hologram-base {
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 15px;
    background: radial-gradient(ellipse, rgba(0, 255, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    animation: hologramPulse 4s ease-in-out infinite;
}

@keyframes hologramPulse {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.7;
    }
}

.ambient-lighting {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.light-beam {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, rgba(0, 255, 255, 0.6), transparent);
    animation: lightBeamMove 6s ease-in-out infinite;
}

.light-beam:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.light-beam:nth-child(2) {
    top: 40%;
    right: 25%;
    animation-delay: 2s;
}

.light-beam:nth-child(3) {
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

@keyframes lightBeamMove {
    0%, 100% {
        opacity: 0;
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: translateY(-20px) rotate(5deg);
    }
}

/* معلومات الروبوت */
.robot-info {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
}

.info-panel {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.info-panel h4 {
    color: #00ffff;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.3rem;
    font-size: 0.8rem;
}

.info-item span:first-child {
    color: #cccccc;
}

.info-item span:last-child {
    color: #00ffff;
    font-weight: 600;
}

/* مؤشرات التفاعل */
.interaction-hints {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 2rem;
}

.hint {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    font-size: 0.9rem;
    animation: hintFloat 3s ease-in-out infinite;
}

.hint-icon {
    font-size: 1.2rem;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes hintFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* معلومات إضافية */
.info-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.overlay-content {
    background: linear-gradient(145deg, #1a1a2e, #16213e);
    border: 2px solid #00ffff;
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 255, 255, 0.3);
}

.overlay-content h2 {
    color: #00ffff;
    margin-bottom: 1rem;
}

.overlay-content h3 {
    color: #00ffff;
    margin: 1.5rem 0 1rem;
}

.overlay-content ul {
    text-align: right;
    margin: 1rem 0;
}

.overlay-content li {
    margin-bottom: 0.5rem;
    color: #cccccc;
}

/* أزرار التحكم السريع */
.quick-controls {
    position: fixed;
    top: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.quick-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.quick-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: #00ffff;
    transform: scale(1.1);
}

/* التجاوب مع الشاشات المختلفة */
@media (max-width: 1200px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .robot-container {
        height: 500px;
    }

    .controls {
        grid-template-columns: 1fr;
    }

    .interaction-hints {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .title-main {
        font-size: 3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
    }

    .robot-container {
        height: 400px;
    }

    .robot-info {
        position: relative;
        top: auto;
        right: auto;
        margin-top: 1rem;
    }

    .quick-controls {
        flex-direction: row;
        top: auto;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 1rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .robot-container {
        height: 300px;
    }

    .interaction-hints {
        bottom: 10px;
    }

    .hint {
        font-size: 0.8rem;
        padding: 8px 15px;
    }

    .control-panel {
        padding: 1rem;
    }

    .status-indicators {
        grid-template-columns: 1fr;
    }
}
