/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
}

/* حاوية الهيرو */
.hero-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1000px;
    overflow: hidden;
}

/* الخلفية المتحركة */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
        radial-gradient(2px 2px at 40px 70px, #ff00ff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ffff00, transparent),
        radial-gradient(1px 1px at 130px 80px, #00ff00, transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: particlesFloat 20s linear infinite;
    opacity: 0.3;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 15s linear infinite;
}

@keyframes particlesFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* محتوى الهيرو */
.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1400px;
    width: 100%;
    padding: 2rem;
    align-items: center;
}

/* قسم النص */
.text-section {
    color: white;
    animation: slideInRight 1s ease-out;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    font-weight: 300;
    color: #00ffff;
    margin-bottom: 0.5rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite, fadeInUp 1s ease-out 0.4s both;
    margin-bottom: 0.5rem;
}

.title-subtitle {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    color: #cccccc;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #b0b0b0;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    animation: fadeInUp 1s ease-out 1s both;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.btn-secondary:hover {
    background: #00ffff;
    color: #0a0a0a;
    transform: translateY(-3px);
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* حاوية الروبوت */
.robot-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
    animation: slideInLeft 1s ease-out;
}

.robot {
    position: relative;
    transform-style: preserve-3d;
    animation: robotFloat 4s ease-in-out infinite;
    transition: transform 0.3s ease;
}

@keyframes robotFloat {
    0%, 100% { transform: translateY(0px) rotateY(0deg); }
    50% { transform: translateY(-20px) rotateY(5deg); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* رأس الروبوت المتقدم */
.robot-head {
    position: relative;
    z-index: 10;
    transform: translateZ(60px);
}

.head-shell {
    position: relative;
    width: 140px;
    height: 120px;
    transform-style: preserve-3d;
    animation: headFloat 4s ease-in-out infinite;
}

@keyframes headFloat {
    0%, 100% { transform: translateY(0px) rotateY(0deg); }
    50% { transform: translateY(-8px) rotateY(2deg); }
}

.head-front {
    position: absolute;
    width: 140px;
    height: 120px;
    background: linear-gradient(145deg, #e8e8e8, #c0c0c0);
    border-radius: 25px 25px 20px 20px;
    transform: translateZ(30px);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.3),
        inset 0 3px 15px rgba(255, 255, 255, 0.4),
        inset 0 -3px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid #a0a0a0;
}

.head-sides {
    position: absolute;
    width: 100%;
    height: 100%;
}

.head-side {
    position: absolute;
    width: 30px;
    height: 120px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 15px;
    box-shadow:
        inset 0 2px 10px rgba(255, 255, 255, 0.3),
        inset 0 -2px 10px rgba(0, 0, 0, 0.2);
}

.head-side.left {
    left: -15px;
    transform: rotateY(-30deg) translateZ(15px);
}

.head-side.right {
    right: -15px;
    transform: rotateY(30deg) translateZ(15px);
}

.head-back {
    position: absolute;
    width: 140px;
    height: 120px;
    background: linear-gradient(145deg, #b0b0b0, #808080);
    border-radius: 25px 25px 20px 20px;
    transform: translateZ(-30px);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.4),
        inset 0 2px 10px rgba(255, 255, 255, 0.2);
}

/* لوحة الوجه */
.face-panel {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 80px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 15px;
    border: 2px solid #404040;
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.2);
}

/* مقابس العيون */
.eye-sockets {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 25px;
}

.eye-socket {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #000, #333);
    border-radius: 50%;
    border: 2px solid #555;
    position: relative;
    box-shadow:
        inset 0 0 15px rgba(0, 0, 0, 0.8),
        0 0 20px rgba(0, 255, 255, 0.3);
}

.eye-lens {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    background: radial-gradient(circle at 30% 30%, #00ffff, #0080ff, #000);
    border-radius: 50%;
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.6),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
    animation: eyeGlow 3s ease-in-out infinite;
}

.eye-iris {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #00ffff, #004080);
    border-radius: 50%;
    animation: irisMove 6s ease-in-out infinite;
}

.eye-reflection {
    position: absolute;
    top: 20%;
    left: 30%;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: reflectionShimmer 2s ease-in-out infinite;
}

@keyframes eyeGlow {
    0%, 100% { box-shadow: 0 0 15px rgba(0, 255, 255, 0.6), inset 0 0 10px rgba(255, 255, 255, 0.2); }
    50% { box-shadow: 0 0 25px rgba(0, 255, 255, 0.9), inset 0 0 15px rgba(255, 255, 255, 0.3); }
}

@keyframes irisMove {
    0%, 100% { transform: translate(-50%, -50%); }
    25% { transform: translate(-40%, -50%); }
    50% { transform: translate(-50%, -40%); }
    75% { transform: translate(-60%, -50%); }
}

@keyframes reflectionShimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* مصفوفة الحساسات */
.sensor-array {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.sensor {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #ff4444, #880000);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.6);
    animation: sensorBlink 2s ease-in-out infinite;
}

.sensor:nth-child(2) {
    animation-delay: 0.5s;
}

.sensor:nth-child(3) {
    animation-delay: 1s;
}

@keyframes sensorBlink {
    0%, 80%, 100% { opacity: 0.3; }
    10%, 70% { opacity: 1; }
}

/* مفصل الرقبة */
.neck-joint {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 30px;
    background: linear-gradient(145deg, #c0c0c0, #808080);
    border-radius: 30px 30px 15px 15px;
    border: 2px solid #606060;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.3),
        inset 0 2px 8px rgba(255, 255, 255, 0.3);
}

.neck-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 8px;
    background: linear-gradient(90deg, #404040, #606060, #404040);
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* جسم الروبوت المتقدم */
.robot-torso {
    position: relative;
    margin-top: 15px;
    z-index: 5;
    transform: translateZ(40px);
}

/* درع الصدر */
.chest-armor {
    position: relative;
    width: 160px;
    height: 180px;
    transform-style: preserve-3d;
    animation: torsoBreath 5s ease-in-out infinite;
}

@keyframes torsoBreath {
    0%, 100% { transform: translateZ(40px) scale(1); }
    50% { transform: translateZ(40px) scale(1.02); }
}

.chest-plate-main {
    position: absolute;
    width: 160px;
    height: 120px;
    background: linear-gradient(145deg, #e0e0e0, #b0b0b0);
    border-radius: 20px 20px 10px 10px;
    border: 3px solid #909090;
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.4),
        inset 0 5px 20px rgba(255, 255, 255, 0.4),
        inset 0 -5px 20px rgba(0, 0, 0, 0.2);
    transform: translateZ(20px);
}

/* نواة الطاقة */
.power-core {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
}

.core-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid;
    animation: coreRotate 8s linear infinite;
}

.core-ring.outer {
    width: 60px;
    height: 60px;
    border-color: rgba(0, 255, 255, 0.6);
    animation-duration: 8s;
}

.core-ring.middle {
    width: 40px;
    height: 40px;
    top: 10px;
    left: 10px;
    border-color: rgba(0, 255, 255, 0.8);
    animation-duration: 6s;
    animation-direction: reverse;
}

.core-ring.inner {
    width: 20px;
    height: 20px;
    top: 20px;
    left: 20px;
    border-color: rgba(0, 255, 255, 1);
    animation-duration: 4s;
}

.core-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #00ffff, #0080ff);
    border-radius: 50%;
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.8),
        inset 0 0 8px rgba(255, 255, 255, 0.5);
    animation: corePulse 2s ease-in-out infinite;
}

@keyframes coreRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes corePulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.8), inset 0 0 8px rgba(255, 255, 255, 0.5);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow: 0 0 30px rgba(0, 255, 255, 1), inset 0 0 12px rgba(255, 255, 255, 0.7);
    }
}

/* فتحات التهوية */
.chest-vents {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.vent {
    width: 25px;
    height: 6px;
    background: linear-gradient(90deg, #404040, #202020, #404040);
    border-radius: 3px;
    border: 1px solid #606060;
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.5),
        0 0 8px rgba(0, 255, 255, 0.2);
    animation: ventGlow 3s ease-in-out infinite;
}

.vent:nth-child(2) { animation-delay: 0.5s; }
.vent:nth-child(3) { animation-delay: 1s; }
.vent:nth-child(4) { animation-delay: 1.5s; }

@keyframes ventGlow {
    0%, 80%, 100% { box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5), 0 0 8px rgba(0, 255, 255, 0.2); }
    10%, 70% { box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5), 0 0 15px rgba(0, 255, 255, 0.5); }
}

/* الألواح الجانبية */
.chest-sides {
    position: absolute;
    width: 100%;
    height: 100%;
}

.side-panel {
    position: absolute;
    width: 40px;
    height: 120px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 10px;
    border: 2px solid #808080;
    box-shadow:
        inset 0 3px 10px rgba(255, 255, 255, 0.3),
        inset 0 -3px 10px rgba(0, 0, 0, 0.2);
}

.side-panel.left {
    left: -20px;
    transform: rotateY(-25deg) translateZ(10px);
}

.side-panel.right {
    right: -20px;
    transform: rotateY(25deg) translateZ(10px);
}

.panel-lines {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 80px;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 8px,
        rgba(0, 0, 0, 0.2) 8px,
        rgba(0, 0, 0, 0.2) 10px
    );
}

/* البطن */
.abdomen {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 60px;
    background: linear-gradient(145deg, #d8d8d8, #a8a8a8);
    border-radius: 15px;
    border: 2px solid #888888;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        inset 0 3px 12px rgba(255, 255, 255, 0.3),
        inset 0 -3px 12px rgba(0, 0, 0, 0.2);
    transform: translateX(-50%) translateZ(15px);
}

.ab-segments {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 8px;
}

.ab-segments .segment {
    width: 25px;
    height: 35px;
    background: linear-gradient(145deg, #c0c0c0, #909090);
    border-radius: 8px;
    border: 1px solid #707070;
    box-shadow:
        inset 0 2px 6px rgba(255, 255, 255, 0.3),
        inset 0 -2px 6px rgba(0, 0, 0, 0.2);
}

/* الأكتاف */
.robot-shoulders {
    position: absolute;
    top: 140px;
    left: 50%;
    transform: translateX(-50%);
    width: 220px;
    height: 60px;
    z-index: 4;
}

.shoulder {
    position: absolute;
    top: 0;
    width: 70px;
    height: 60px;
}

.left-shoulder {
    left: 0;
    animation: shoulderMove 6s ease-in-out infinite;
}

.right-shoulder {
    right: 0;
    animation: shoulderMove 6s ease-in-out infinite reverse;
}

@keyframes shoulderMove {
    0%, 100% { transform: rotateZ(-5deg); }
    50% { transform: rotateZ(5deg); }
}

.shoulder-joint {
    position: relative;
    width: 50px;
    height: 50px;
    background: linear-gradient(145deg, #c8c8c8, #989898);
    border-radius: 50%;
    border: 3px solid #787878;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.3);
    transform: translateZ(15px);
}

.joint-ball {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: radial-gradient(circle at 30% 30%, #f0f0f0, #a0a0a0);
    border-radius: 50%;
    border: 2px solid #606060;
    box-shadow:
        inset 0 2px 8px rgba(255, 255, 255, 0.5),
        inset 0 -2px 8px rgba(0, 0, 0, 0.3);
}

.shoulder-armor {
    position: absolute;
    top: -10px;
    left: 10px;
    width: 50px;
    height: 70px;
    background: linear-gradient(145deg, #e8e8e8, #b8b8b8);
    border-radius: 25px 25px 15px 15px;
    border: 2px solid #888888;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.2);
    transform: translateZ(10px);
}

/* الأذرع المتقدمة */
.robot-arms {
    position: absolute;
    top: 160px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 200px;
    z-index: 3;
}

.arm {
    position: absolute;
    top: 0;
    transform-origin: top center;
    transform-style: preserve-3d;
}

.left-arm {
    left: 20px;
    animation: leftArmSway 7s ease-in-out infinite;
}

.right-arm {
    right: 20px;
    animation: rightArmSway 7s ease-in-out infinite;
}

@keyframes leftArmSway {
    0%, 100% { transform: rotateZ(-15deg) rotateX(10deg); }
    50% { transform: rotateZ(-25deg) rotateX(-5deg); }
}

@keyframes rightArmSway {
    0%, 100% { transform: rotateZ(15deg) rotateX(10deg); }
    50% { transform: rotateZ(25deg) rotateX(-5deg); }
}

/* الذراع العلوي */
.upper-arm {
    position: relative;
    width: 35px;
    height: 80px;
    transform-style: preserve-3d;
}

.arm-shell {
    position: absolute;
    width: 35px;
    height: 80px;
    background: linear-gradient(145deg, #e0e0e0, #b0b0b0);
    border-radius: 17px;
    border: 2px solid #909090;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.2);
    transform: translateZ(8px);
}

.arm-segments {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.arm-segments .segment {
    width: 25px;
    height: 20px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 10px;
    border: 1px solid #808080;
    box-shadow:
        inset 0 2px 6px rgba(255, 255, 255, 0.3),
        inset 0 -2px 6px rgba(0, 0, 0, 0.2);
}

/* مفصل الكوع */
.elbow-joint {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background: linear-gradient(145deg, #c8c8c8, #989898);
    border-radius: 20px;
    border: 2px solid #787878;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.3),
        inset 0 2px 8px rgba(255, 255, 255, 0.4),
        inset 0 -2px 8px rgba(0, 0, 0, 0.3);
}

.joint-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 8px;
    background: linear-gradient(90deg, #505050, #707070, #505050);
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* الساعد */
.forearm {
    position: absolute;
    top: 95px;
    left: 0;
    width: 30px;
    height: 70px;
    transform-style: preserve-3d;
    animation: forearmRotate 5s ease-in-out infinite;
}

@keyframes forearmRotate {
    0%, 100% { transform: rotateX(0deg); }
    50% { transform: rotateX(15deg); }
}

.forearm-shell {
    position: absolute;
    width: 30px;
    height: 70px;
    background: linear-gradient(145deg, #d8d8d8, #a8a8a8);
    border-radius: 15px;
    border: 2px solid #888888;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 3px 10px rgba(255, 255, 255, 0.4),
        inset 0 -3px 10px rgba(0, 0, 0, 0.2);
    transform: translateZ(6px);
}

.forearm-details {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.detail-line {
    width: 20px;
    height: 3px;
    background: linear-gradient(90deg, #606060, #808080, #606060);
    border-radius: 1.5px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* مفصل المعصم */
.wrist-joint {
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 24px;
    background: linear-gradient(145deg, #c0c0c0, #909090);
    border-radius: 17px;
    border: 2px solid #707070;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 2px 6px rgba(255, 255, 255, 0.4),
        inset 0 -2px 6px rgba(0, 0, 0, 0.3);
}

.wrist-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 6px;
    background: linear-gradient(90deg, #404040, #606060, #404040);
    border-radius: 3px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* اليد المتقدمة */
.hand {
    position: absolute;
    top: 175px;
    left: 2.5px;
    width: 25px;
    height: 35px;
    transform-style: preserve-3d;
    animation: handMove 4s ease-in-out infinite;
}

@keyframes handMove {
    0%, 100% { transform: rotateX(0deg) rotateZ(0deg); }
    50% { transform: rotateX(10deg) rotateZ(5deg); }
}

.palm {
    position: relative;
    width: 25px;
    height: 20px;
    background: linear-gradient(145deg, #e0e0e0, #b0b0b0);
    border-radius: 12px 12px 8px 8px;
    border: 2px solid #909090;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 2px 8px rgba(255, 255, 255, 0.4),
        inset 0 -2px 8px rgba(0, 0, 0, 0.2);
    transform: translateZ(4px);
}

.palm-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 8px;
    background: linear-gradient(145deg, #c8c8c8, #989898);
    border-radius: 6px;
    border: 1px solid #787878;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* الأصابع */
.fingers {
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 100%;
    height: 15px;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
}

.finger {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1px;
    animation: fingerFlex 3s ease-in-out infinite;
}

.finger.thumb {
    position: absolute;
    left: -8px;
    top: -5px;
    transform: rotate(-30deg);
    animation-delay: 0s;
}

.finger.index { animation-delay: 0.2s; }
.finger.middle { animation-delay: 0.4s; }
.finger.ring { animation-delay: 0.6s; }
.finger.pinky { animation-delay: 0.8s; }

@keyframes fingerFlex {
    0%, 100% { transform: rotateX(0deg); }
    50% { transform: rotateX(20deg); }
}

.finger-segment {
    width: 4px;
    height: 6px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 2px;
    border: 1px solid #808080;
    box-shadow:
        inset 0 1px 2px rgba(255, 255, 255, 0.3),
        inset 0 -1px 2px rgba(0, 0, 0, 0.2);
}

.finger.thumb .finger-segment {
    width: 5px;
    height: 7px;
}

.finger.middle .finger-segment {
    height: 7px;
}

.finger.pinky .finger-segment {
    width: 3px;
    height: 5px;
}

/* الوركين */
.robot-hips {
    position: absolute;
    top: 320px;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
    height: 40px;
    z-index: 2;
}

.hip-joint {
    position: absolute;
    top: 0;
    width: 50px;
    height: 40px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 25px;
    border: 2px solid #808080;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 3px 10px rgba(255, 255, 255, 0.4),
        inset 0 -3px 10px rgba(0, 0, 0, 0.2);
    transform: translateZ(12px);
}

.left-hip {
    left: 20px;
    animation: hipMove 6s ease-in-out infinite;
}

.right-hip {
    right: 20px;
    animation: hipMove 6s ease-in-out infinite reverse;
}

@keyframes hipMove {
    0%, 100% { transform: translateZ(12px) rotateZ(-2deg); }
    50% { transform: translateZ(12px) rotateZ(2deg); }
}

.hip-socket {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 20px;
    background: radial-gradient(ellipse, #f0f0f0, #a0a0a0);
    border-radius: 50%;
    border: 2px solid #606060;
    box-shadow:
        inset 0 2px 6px rgba(255, 255, 255, 0.5),
        inset 0 -2px 6px rgba(0, 0, 0, 0.3);
}

/* الأرجل المتقدمة */
.robot-legs {
    position: absolute;
    top: 370px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 200px;
    z-index: 1;
}

.leg {
    position: absolute;
    top: 0;
    transform-origin: top center;
    transform-style: preserve-3d;
}

.left-leg {
    left: 10px;
    animation: leftLegStand 8s ease-in-out infinite;
}

.right-leg {
    right: 10px;
    animation: rightLegStand 8s ease-in-out infinite;
}

@keyframes leftLegStand {
    0%, 100% { transform: rotateZ(-3deg) rotateX(5deg); }
    50% { transform: rotateZ(3deg) rotateX(-2deg); }
}

@keyframes rightLegStand {
    0%, 100% { transform: rotateZ(3deg) rotateX(5deg); }
    50% { transform: rotateZ(-3deg) rotateX(-2deg); }
}

/* الفخذ */
.thigh {
    position: relative;
    width: 40px;
    height: 90px;
    transform-style: preserve-3d;
}

.thigh-armor {
    position: absolute;
    width: 40px;
    height: 90px;
    background: linear-gradient(145deg, #e0e0e0, #b0b0b0);
    border-radius: 20px;
    border: 3px solid #909090;
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.4),
        inset 0 4px 15px rgba(255, 255, 255, 0.4),
        inset 0 -4px 15px rgba(0, 0, 0, 0.2);
    transform: translateZ(10px);
}

.thigh-segments {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.thigh-segments .segment {
    width: 30px;
    height: 25px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 12px;
    border: 2px solid #808080;
    box-shadow:
        inset 0 2px 8px rgba(255, 255, 255, 0.3),
        inset 0 -2px 8px rgba(0, 0, 0, 0.2);
}

/* مفصل الركبة */
.knee-joint {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 45px;
    height: 40px;
    background: linear-gradient(145deg, #c8c8c8, #989898);
    border-radius: 22px;
    border: 3px solid #787878;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.3);
    transform: translateZ(8px);
}

.knee-cap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 20px;
    background: radial-gradient(ellipse at 30% 30%, #f0f0f0, #a0a0a0);
    border-radius: 50%;
    border: 2px solid #606060;
    box-shadow:
        inset 0 2px 8px rgba(255, 255, 255, 0.5),
        inset 0 -2px 8px rgba(0, 0, 0, 0.3);
}

/* الساق */
.shin {
    position: absolute;
    top: 110px;
    left: 0;
    width: 35px;
    height: 80px;
    transform-style: preserve-3d;
}

.shin-armor {
    position: absolute;
    width: 35px;
    height: 80px;
    background: linear-gradient(145deg, #d8d8d8, #a8a8a8);
    border-radius: 17px;
    border: 2px solid #888888;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.2);
    transform: translateZ(8px);
}

.shin-details {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.shin-details .detail {
    width: 25px;
    height: 4px;
    background: linear-gradient(90deg, #606060, #808080, #606060);
    border-radius: 2px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* مفصل الكاحل */
.ankle-joint {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background: linear-gradient(145deg, #c0c0c0, #909090);
    border-radius: 20px;
    border: 2px solid #707070;
    box-shadow:
        0 6px 18px rgba(0, 0, 0, 0.3),
        inset 0 2px 8px rgba(255, 255, 255, 0.4),
        inset 0 -2px 8px rgba(0, 0, 0, 0.3);
}

.ankle-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 8px;
    background: linear-gradient(90deg, #404040, #606060, #404040);
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* القدم */
.foot {
    position: absolute;
    top: 205px;
    left: -5px;
    width: 45px;
    height: 25px;
    transform-style: preserve-3d;
}

.foot-base {
    position: relative;
    width: 45px;
    height: 25px;
    background: linear-gradient(145deg, #e0e0e0, #b0b0b0);
    border-radius: 22px 22px 8px 8px;
    border: 2px solid #909090;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 3px 10px rgba(255, 255, 255, 0.4),
        inset 0 -3px 10px rgba(0, 0, 0, 0.2);
    transform: translateZ(6px);
}

.heel {
    position: absolute;
    top: 50%;
    left: 5px;
    transform: translateY(-50%);
    width: 12px;
    height: 15px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 6px;
    border: 1px solid #808080;
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
}

.arch {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 12px;
    background: linear-gradient(145deg, #c8c8c8, #989898);
    border-radius: 10px;
    border: 1px solid #787878;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toes {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    display: flex;
    gap: 2px;
}

.toe {
    width: 6px;
    height: 10px;
    background: linear-gradient(145deg, #d0d0d0, #a0a0a0);
    border-radius: 3px;
    border: 1px solid #808080;
    box-shadow:
        inset 0 1px 2px rgba(255, 255, 255, 0.3),
        inset 0 -1px 2px rgba(0, 0, 0, 0.2);
}

/* التأثيرات الإضافية المحدثة */
.robot-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 50%;
    animation: energyFieldRotate 12s linear infinite;
}

.energy-field::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 320px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: energyFieldRotate 8s linear infinite reverse;
}

.energy-field::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 240px;
    height: 240px;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: energyFieldRotate 6s linear infinite;
}

@keyframes energyFieldRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); opacity: 0.2; }
    50% { opacity: 0.5; }
    100% { transform: translate(-50%, -50%) rotate(360deg); opacity: 0.2; }
}

.hologram-base {
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 15px;
    background: radial-gradient(ellipse, rgba(0, 255, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    animation: hologramPulse 4s ease-in-out infinite;
}

@keyframes hologramPulse {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.7;
    }
}

.ambient-lighting {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.light-beam {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, rgba(0, 255, 255, 0.6), transparent);
    animation: lightBeamMove 6s ease-in-out infinite;
}

.light-beam:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.light-beam:nth-child(2) {
    top: 40%;
    right: 25%;
    animation-delay: 2s;
}

.light-beam:nth-child(3) {
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

@keyframes lightBeamMove {
    0%, 100% {
        opacity: 0;
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: translateY(-20px) rotate(5deg);
    }
}

/* مؤشرات التفاعل */
.interaction-hints {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.hint {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    font-size: 0.9rem;
    animation: hintFloat 3s ease-in-out infinite;
}

.hint-icon {
    font-size: 1.2rem;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes hintFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* التجاوب مع الشاشات المختلفة */
@media (max-width: 1200px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .robot-container {
        height: 500px;
    }

    .robot {
        transform: scale(0.8);
    }
}

@media (max-width: 768px) {
    .title-main {
        font-size: 3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
    }

    .robot {
        transform: scale(0.6);
    }

    .robot-container {
        height: 400px;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 1rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .robot {
        transform: scale(0.5);
    }

    .robot-container {
        height: 300px;
    }

    .interaction-hints {
        bottom: 10px;
    }

    .hint {
        font-size: 0.8rem;
        padding: 8px 15px;
    }
}
