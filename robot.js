class AdvancedRobot {
    constructor() {
        this.windowWidth = window.innerWidth;
        this.windowHeight = window.innerHeight;

        // إعدادات الأنيميشن
        this.animation = {
            flyingHeight: 2,
            flyingFreq: 0.015,
            eyeAmplitude: 2,
            eyelidAmplitude: 1,
            modelsAmplitude: 0.3,
            reactionTime: 0.1,
            speed: 0.5,
            eyelidsOpening: 0,
            flying: true,
            eyeSensitivity: 1,
            lightingIntensity: 1
        };

        // حالة الروبوت
        this.robotState = {
            isActive: true,
            isLoaded: false,
            introComplete: false,
            coef: 0,
            height: 0
        };

        // ربط الوظائف
        this.onWindowResize = this.onWindowResize.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.render = this.render.bind(this);
        this.onCanvasClick = this.onCanvasClick.bind(this);

        // الأحداث
        window.addEventListener('resize', this.onWindowResize);
        window.addEventListener('mousemove', this.onMouseMove);

        this.init();
    }

    /**
     * تهيئة الروبوت
     */
    init() {
        this.scene = new THREE.Scene();
        this.mouse = new THREE.Vector2(0, 0);
        this.clock = new THREE.Clock();

        this.setupRenderer();
        this.setupCamera();
        this.setupLights();
        this.setupGround();
        this.createRobot();
        this.setupControls();
        this.render();
    }

    /**
     * إعداد المُعرِض
     */
    setupRenderer() {
        const canvas = document.getElementById('robot-canvas');
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            alpha: true,
            antialias: true
        });

        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.setClearColor(0x000000, 0);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;

        // إضافة مستمع النقر
        canvas.addEventListener('click', this.onCanvasClick);
    }

    /**
     * إعداد الكاميرا
     */
    setupCamera() {
        const canvas = document.getElementById('robot-canvas');
        const aspect = canvas.clientWidth / canvas.clientHeight;
        const fieldOfView = 45;
        const near = 0.1;
        const far = 1000;

        this.camera = new THREE.PerspectiveCamera(fieldOfView, aspect, near, far);
        this.camera.position.set(0, 2, 8);
        this.camera.lookAt(0, 0, 0);
    }

    /**
     * إعداد الإضاءة
     */
    setupLights() {
        // إضاءة محيطة
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // إضاءة اتجاهية رئيسية
        this.mainLight = new THREE.DirectionalLight(0xffffff, 1);
        this.mainLight.position.set(5, 10, 5);
        this.mainLight.castShadow = true;
        this.mainLight.shadow.mapSize.width = 2048;
        this.mainLight.shadow.mapSize.height = 2048;
        this.mainLight.shadow.camera.near = 0.5;
        this.mainLight.shadow.camera.far = 50;
        this.mainLight.shadow.camera.left = -10;
        this.mainLight.shadow.camera.right = 10;
        this.mainLight.shadow.camera.top = 10;
        this.mainLight.shadow.camera.bottom = -10;
        this.scene.add(this.mainLight);

        // إضاءة ملونة للتأثير
        this.accentLight1 = new THREE.PointLight(0x00ffff, 0.5, 10);
        this.accentLight1.position.set(-3, 3, 3);
        this.scene.add(this.accentLight1);

        this.accentLight2 = new THREE.PointLight(0xff00ff, 0.5, 10);
        this.accentLight2.position.set(3, 3, -3);
        this.scene.add(this.accentLight2);
    }

    /**
     * إعداد الأرضية
     */
    setupGround() {
        const geometry = new THREE.PlaneGeometry(20, 20);
        const material = new THREE.MeshLambertMaterial({
            color: 0x333333,
            transparent: true,
            opacity: 0.3
        });

        this.ground = new THREE.Mesh(geometry, material);
        this.ground.rotation.x = -Math.PI / 2;
        this.ground.position.y = -3;
        this.ground.receiveShadow = true;
        this.scene.add(this.ground);
    }

    /**
     * إنشاء الروبوت
     */
    createRobot() {
        this.robotGroup = new THREE.Group();

        // إنشاء أجزاء الروبوت
        this.createHead();
        this.createBody();
        this.createArms();
        this.createLegs();

        // إضافة الروبوت للمشهد
        this.scene.add(this.robotGroup);

        // تعيين الموضع الأولي
        this.robotGroup.position.y = this.animation.flyingHeight;
        this.robotState.height = this.robotGroup.position.y;

        // بدء الأنيميشن التعريفي
        this.startIntroAnimation();
    }

    /**
     * إنشاء الرأس
     */
    createHead() {
        this.headGroup = new THREE.Group();

        // الرأس الرئيسي
        const headGeometry = new THREE.BoxGeometry(1.2, 1, 1);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xe0e0e0,
            shininess: 100
        });
        this.head = new THREE.Mesh(headGeometry, headMaterial);
        this.head.position.y = 2;
        this.head.castShadow = true;
        this.headGroup.add(this.head);

        // العيون
        this.createEyes();

        // الفم
        this.createMouth();

        // الهوائيات
        this.createAntennas();

        this.robotGroup.add(this.headGroup);
    }

    /**
     * إنشاء العيون
     */
    createEyes() {
        this.eyeGroup = new THREE.Group();

        // العين اليسرى
        const eyeGeometry = new THREE.SphereGeometry(0.15, 16, 16);
        const eyeMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ffff,
            emissive: 0x004444,
            shininess: 100
        });

        this.leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        this.leftEye.position.set(-0.3, 2.1, 0.4);
        this.leftEye.castShadow = true;

        this.rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        this.rightEye.position.set(0.3, 2.1, 0.4);
        this.rightEye.castShadow = true;

        // إضاءة العيون
        const eyeLight1 = new THREE.PointLight(0x00ffff, 0.5, 2);
        eyeLight1.position.copy(this.leftEye.position);

        const eyeLight2 = new THREE.PointLight(0x00ffff, 0.5, 2);
        eyeLight2.position.copy(this.rightEye.position);

        this.eyeGroup.add(this.leftEye);
        this.eyeGroup.add(this.rightEye);
        this.eyeGroup.add(eyeLight1);
        this.eyeGroup.add(eyeLight2);

        this.robotGroup.add(this.eyeGroup);
    }

    /**
     * إنشاء الفم
     */
    createMouth() {
        const mouthGeometry = new THREE.BoxGeometry(0.4, 0.05, 0.1);
        const mouthMaterial = new THREE.MeshPhongMaterial({
            color: 0x333333,
            emissive: 0x001111
        });

        this.mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
        this.mouth.position.set(0, 1.7, 0.45);
        this.robotGroup.add(this.mouth);
    }

    /**
     * إنشاء الهوائيات
     */
    createAntennas() {
        const antennaGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.5);
        const antennaMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 });

        // الهوائي الأيسر
        this.leftAntenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
        this.leftAntenna.position.set(-0.4, 2.7, 0);

        // الهوائي الأيمن
        this.rightAntenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
        this.rightAntenna.position.set(0.4, 2.7, 0);

        // كرات الهوائي
        const ballGeometry = new THREE.SphereGeometry(0.08, 8, 8);
        const ballMaterial = new THREE.MeshPhongMaterial({
            color: 0xff00ff,
            emissive: 0x440044
        });

        const leftBall = new THREE.Mesh(ballGeometry, ballMaterial);
        leftBall.position.set(-0.4, 3, 0);

        const rightBall = new THREE.Mesh(ballGeometry, ballMaterial);
        rightBall.position.set(0.4, 3, 0);

        this.robotGroup.add(this.leftAntenna);
        this.robotGroup.add(this.rightAntenna);
        this.robotGroup.add(leftBall);
        this.robotGroup.add(rightBall);
    }

    /**
     * إنشاء الجسم
     */
    createBody() {
        const bodyGeometry = new THREE.BoxGeometry(1.5, 2, 0.8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0xd0d0d0,
            shininess: 80
        });

        this.body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        this.body.position.y = 0.5;
        this.body.castShadow = true;

        // لوحة الصدر
        this.createChestPanel();

        this.robotGroup.add(this.body);
    }

    /**
     * إنشاء لوحة الصدر
     */
    createChestPanel() {
        const panelGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.1);
        const panelMaterial = new THREE.MeshPhongMaterial({
            color: 0x333333,
            emissive: 0x001111
        });

        this.chestPanel = new THREE.Mesh(panelGeometry, panelMaterial);
        this.chestPanel.position.set(0, 0.7, 0.45);

        // نواة الطاقة
        const coreGeometry = new THREE.SphereGeometry(0.15, 16, 16);
        const coreMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ffff,
            emissive: 0x004444,
            transparent: true,
            opacity: 0.8
        });

        this.powerCore = new THREE.Mesh(coreGeometry, coreMaterial);
        this.powerCore.position.set(0, 0.9, 0.5);

        // إضاءة نواة الطاقة
        this.coreLight = new THREE.PointLight(0x00ffff, 1, 3);
        this.coreLight.position.copy(this.powerCore.position);

        this.robotGroup.add(this.chestPanel);
        this.robotGroup.add(this.powerCore);
        this.robotGroup.add(this.coreLight);
    }

    /**
     * إنشاء الأذرع
     */
    createArms() {
        this.armsGroup = new THREE.Group();

        // الذراع الأيسر
        this.leftArm = this.createArm(-1);
        this.leftArm.position.set(-1, 1, 0);

        // الذراع الأيمن
        this.rightArm = this.createArm(1);
        this.rightArm.position.set(1, 1, 0);

        this.armsGroup.add(this.leftArm);
        this.armsGroup.add(this.rightArm);
        this.robotGroup.add(this.armsGroup);
    }

    /**
     * إنشاء ذراع واحد
     */
    createArm(side) {
        const armGroup = new THREE.Group();

        // الذراع العلوي
        const upperArmGeometry = new THREE.CylinderGeometry(0.15, 0.15, 1);
        const armMaterial = new THREE.MeshPhongMaterial({ color: 0xc0c0c0 });

        const upperArm = new THREE.Mesh(upperArmGeometry, armMaterial);
        upperArm.position.y = -0.3;
        upperArm.castShadow = true;

        // الذراع السفلي
        const lowerArm = new THREE.Mesh(upperArmGeometry, armMaterial);
        lowerArm.position.y = -1;
        lowerArm.castShadow = true;

        // اليد
        const handGeometry = new THREE.SphereGeometry(0.2, 12, 12);
        const hand = new THREE.Mesh(handGeometry, armMaterial);
        hand.position.y = -1.7;
        hand.castShadow = true;

        armGroup.add(upperArm);
        armGroup.add(lowerArm);
        armGroup.add(hand);

        return armGroup;
    }

    /**
     * إنشاء الأرجل
     */
    createLegs() {
        this.legsGroup = new THREE.Group();

        // الرجل اليسرى
        this.leftLeg = this.createLeg();
        this.leftLeg.position.set(-0.4, -0.5, 0);

        // الرجل اليمنى
        this.rightLeg = this.createLeg();
        this.rightLeg.position.set(0.4, -0.5, 0);

        this.legsGroup.add(this.leftLeg);
        this.legsGroup.add(this.rightLeg);
        this.robotGroup.add(this.legsGroup);
    }

    /**
     * إنشاء رجل واحدة
     */
    createLeg() {
        const legGroup = new THREE.Group();

        // الفخذ
        const thighGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1.2);
        const legMaterial = new THREE.MeshPhongMaterial({ color: 0xb0b0b0 });

        const thigh = new THREE.Mesh(thighGeometry, legMaterial);
        thigh.position.y = -0.3;
        thigh.castShadow = true;

        // الساق
        const shin = new THREE.Mesh(thighGeometry, legMaterial);
        shin.position.y = -1.2;
        shin.castShadow = true;

        // القدم
        const footGeometry = new THREE.BoxGeometry(0.4, 0.2, 0.6);
        const foot = new THREE.Mesh(footGeometry, legMaterial);
        foot.position.y = -2;
        foot.castShadow = true;

        legGroup.add(thigh);
        legGroup.add(shin);
        legGroup.add(foot);

        return legGroup;
    }

    /**
     * بدء الأنيميشن التعريفي
     */
    startIntroAnimation() {
        // إخفاء شاشة التحميل
        const loadingScreen = document.getElementById('loading-screen');
        gsap.to(loadingScreen, {
            opacity: 0,
            duration: 0.5,
            onComplete: () => {
                loadingScreen.style.display = 'none';
            }
        });

        // أنيميشن الدخول
        gsap.set(this.robotGroup.position, { y: 10 });
        gsap.set(this.robotGroup.rotation, { y: Math.PI * 2 });

        gsap.to(this.robotGroup.position, {
            y: this.animation.flyingHeight,
            duration: 2,
            ease: "bounce.out"
        });

        gsap.to(this.robotGroup.rotation, {
            y: 0,
            duration: 2,
            ease: "power2.out",
            onComplete: () => {
                this.robotState.introComplete = true;
                this.updateStatus('جاهز');
            }
        });

        this.robotState.isLoaded = true;
    }

    /**
     * إعداد عناصر التحكم
     */
    setupControls() {
        // أزرار التحكم
        const activateBtn = document.getElementById('activate-btn');
        const resetBtn = document.getElementById('reset-btn');

        if (activateBtn) {
            activateBtn.addEventListener('click', () => this.toggleRobot());
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetRobot());
        }

        // عناصر التحكم بالمنزلقات
        const speedControl = document.getElementById('speed-control');
        const eyeSensitivity = document.getElementById('eye-sensitivity');
        const flyingHeight = document.getElementById('flying-height');
        const lighting = document.getElementById('lighting');

        if (speedControl) {
            speedControl.addEventListener('input', (e) => {
                this.animation.speed = parseFloat(e.target.value);
            });
        }

        if (eyeSensitivity) {
            eyeSensitivity.addEventListener('input', (e) => {
                this.animation.eyeSensitivity = parseFloat(e.target.value);
            });
        }

        if (flyingHeight) {
            flyingHeight.addEventListener('input', (e) => {
                this.animation.flyingHeight = parseFloat(e.target.value);
            });
        }

        if (lighting) {
            lighting.addEventListener('input', (e) => {
                this.animation.lightingIntensity = parseFloat(e.target.value);
                this.updateLighting();
            });
        }

        // الأزرار السريعة
        const infoBtn = document.getElementById('info-btn');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const screenshotBtn = document.getElementById('screenshot-btn');

        if (infoBtn) {
            infoBtn.addEventListener('click', () => this.showInfo());
        }

        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }

        if (screenshotBtn) {
            screenshotBtn.addEventListener('click', () => this.takeScreenshot());
        }
    }

    /**
     * تبديل حالة الروبوت
     */
    toggleRobot() {
        this.robotState.isActive = !this.robotState.isActive;

        if (this.robotState.isActive) {
            this.activateRobot();
        } else {
            this.deactivateRobot();
        }
    }

    /**
     * تفعيل الروبوت
     */
    activateRobot() {
        this.animation.flying = true;

        // تحديث الإضاءة
        gsap.to(this.coreLight, { intensity: 1, duration: 0.5 });
        gsap.to(this.powerCore.material, { opacity: 0.8, duration: 0.5 });

        // تحديث العيون
        gsap.to(this.leftEye.material, { emissive: { r: 0, g: 0.3, b: 0.3 }, duration: 0.5 });
        gsap.to(this.rightEye.material, { emissive: { r: 0, g: 0.3, b: 0.3 }, duration: 0.5 });

        this.updateStatus('نشط');
        this.updatePowerStatus('نشط');
    }

    /**
     * إلغاء تفعيل الروبوت
     */
    deactivateRobot() {
        this.animation.flying = false;

        // إيقاف الإضاءة
        gsap.to(this.coreLight, { intensity: 0.2, duration: 0.5 });
        gsap.to(this.powerCore.material, { opacity: 0.3, duration: 0.5 });

        // إطفاء العيون
        gsap.to(this.leftEye.material, { emissive: { r: 0, g: 0, b: 0 }, duration: 0.5 });
        gsap.to(this.rightEye.material, { emissive: { r: 0, g: 0, b: 0 }, duration: 0.5 });

        // إنزال الروبوت
        gsap.to(this.robotGroup.position, { y: -1, duration: 1, ease: "power2.out" });

        this.updateStatus('متوقف');
        this.updatePowerStatus('متوقف');
    }

    /**
     * إعادة تعيين الروبوت
     */
    resetRobot() {
        // إعادة تعيين الموضع
        gsap.to(this.robotGroup.position, {
            x: 0,
            y: this.animation.flyingHeight,
            z: 0,
            duration: 1
        });

        gsap.to(this.robotGroup.rotation, {
            x: 0,
            y: 0,
            z: 0,
            duration: 1
        });

        // إعادة تعيين العيون
        gsap.to(this.eyeGroup.rotation, {
            x: 0,
            y: 0,
            z: 0,
            duration: 0.5
        });

        this.robotState.coef = 0;
        this.updateStatus('إعادة تعيين');

        setTimeout(() => {
            this.updateStatus('جاهز');
        }, 1000);
    }

    /**
     * أنيميشن الطيران
     */
    flyingAnimation() {
        if (!this.animation.flying) return;

        this.robotState.coef += this.animation.flyingFreq;
        const c = Math.sin(Math.PI * this.robotState.coef);

        // حركة الطيران
        this.robotGroup.position.y = this.animation.flyingHeight + (c * this.animation.modelsAmplitude);

        // حركة العيون
        this.eyeGroup.position.y = c * this.animation.eyeAmplitude * 0.1;

        // حركة الأذرع
        this.leftArm.rotation.z = Math.sin(this.robotState.coef * 2) * 0.2;
        this.rightArm.rotation.z = -Math.sin(this.robotState.coef * 2) * 0.2;

        // نبض نواة الطاقة
        const pulseIntensity = 0.8 + Math.sin(this.robotState.coef * 4) * 0.2;
        this.coreLight.intensity = pulseIntensity * this.animation.lightingIntensity;
    }

    /**
     * أنيميشن تفاعل الماوس
     */
    mouseAnimation() {
        if (!this.robotState.introComplete) return;

        const targetRotationY = this.mouse.x * this.animation.eyeSensitivity * 0.5;
        const targetRotationX = -this.mouse.y * this.animation.eyeSensitivity * 0.3;

        // تحريك العيون
        gsap.to(this.eyeGroup.rotation, {
            x: targetRotationX,
            y: targetRotationY,
            duration: this.animation.speed,
            ease: "power2.out"
        });

        // تحريك الرأس
        gsap.to(this.headGroup.rotation, {
            x: targetRotationX * 0.3,
            y: targetRotationY * 0.5,
            duration: this.animation.speed,
            ease: "power2.out"
        });

        // تحريك الجسم قليلاً
        gsap.to(this.robotGroup.rotation, {
            x: -this.mouse.y * 0.1,
            y: this.mouse.x * 0.2,
            duration: this.animation.speed * 2,
            ease: "power2.out"
        });
    }

    /**
     * تحديث الإضاءة
     */
    updateLighting() {
        this.mainLight.intensity = this.animation.lightingIntensity;
        this.accentLight1.intensity = this.animation.lightingIntensity * 0.5;
        this.accentLight2.intensity = this.animation.lightingIntensity * 0.5;
    }

    /**
     * تحديث حالة الروبوت
     */
    updateStatus(status) {
        const statusElement = document.getElementById('robot-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * تحديث حالة الطاقة
     */
    updatePowerStatus(status) {
        const powerElement = document.getElementById('power-status');
        if (powerElement) {
            powerElement.textContent = status;
        }
    }

    /**
     * تحديث عداد FPS
     */
    updateFPS() {
        const fpsElement = document.getElementById('fps-counter');
        if (fpsElement) {
            const fps = Math.round(1 / this.clock.getDelta());
            fpsElement.textContent = fps;
        }
    }

    /**
     * عرض المعلومات
     */
    showInfo() {
        const infoOverlay = document.getElementById('info-overlay');
        if (infoOverlay) {
            infoOverlay.style.display = 'flex';
        }
    }

    /**
     * تبديل ملء الشاشة
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * التقاط لقطة شاشة
     */
    takeScreenshot() {
        const canvas = this.renderer.domElement;
        const link = document.createElement('a');
        link.download = 'rebot-3d-screenshot.png';
        link.href = canvas.toDataURL();
        link.click();
    }

    /**
     * النقر على الكانفاس
     */
    onCanvasClick(event) {
        // تأثير النقر
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            left: ${event.offsetX}px;
            top: ${event.offsetY}px;
            width: 20px;
            height: 20px;
            background: rgba(0, 255, 255, 0.5);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            animation: rippleEffect 0.6s ease-out forwards;
        `;

        event.target.parentElement.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);

        // تفعيل/إلغاء تفعيل الروبوت
        this.toggleRobot();
    }

    /**
     * حدث حركة الماوس
     */
    onMouseMove(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        this.mouseAnimation();
    }

    /**
     * حدث تغيير حجم النافذة
     */
    onWindowResize() {
        const canvas = document.getElementById('robot-canvas');
        this.windowWidth = canvas.clientWidth;
        this.windowHeight = canvas.clientHeight;

        this.renderer.setSize(this.windowWidth, this.windowHeight);
        this.camera.aspect = this.windowWidth / this.windowHeight;
        this.camera.updateProjectionMatrix();
    }

    /**
     * حلقة الرندر
     */
    render() {
        requestAnimationFrame(this.render);

        if (this.robotState.isLoaded) {
            this.flyingAnimation();
            this.updateFPS();
        }

        this.renderer.render(this.scene, this.camera);
    }
}

// إضافة تأثير الموجة للنقر
const style = document.createElement('style');
style.textContent = `
    @keyframes rippleEffect {
        0% {
            transform: translate(-50%, -50%) scale(0);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) scale(10);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
