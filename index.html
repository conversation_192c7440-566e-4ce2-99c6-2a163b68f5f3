<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REBOT - روبوت ثلاثي الأبعاد متقدم</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p id="loading-text">جاري تحميل الروبوت...</p>
            <div class="loading-progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="hero-container">
        <!-- خلفية متحركة -->
        <div class="background-animation">
            <div class="floating-particles"></div>
            <div class="grid-overlay"></div>
        </div>

        <!-- محتوى الهيرو -->
        <div class="hero-content">
            <div class="text-section">
                <h1 class="hero-title">
                    <span class="title-line">مرحباً بك في</span>
                    <span class="title-main">REBOT 3D</span>
                    <span class="title-subtitle">روبوت ثلاثي الأبعاد متقدم</span>
                </h1>
                <p class="hero-description">
                    اكتشف عالماً جديداً من التكنولوجيا المتقدمة والذكاء الاصطناعي
                    مع روبوتنا الثلاثي الأبعاد التفاعلي المبني بتقنية WebGL
                </p>
                <div class="hero-buttons">
                    <button class="btn btn-primary" id="activate-btn">تفعيل الروبوت</button>
                    <button class="btn btn-secondary" id="reset-btn">إعادة تعيين</button>
                </div>
                
                <!-- لوحة التحكم -->
                <div class="control-panel">
                    <h3>لوحة التحكم</h3>
                    <div class="controls">
                        <div class="control-group">
                            <label>سرعة الحركة</label>
                            <input type="range" id="speed-control" min="0.1" max="2" step="0.1" value="1">
                        </div>
                        <div class="control-group">
                            <label>حساسية العيون</label>
                            <input type="range" id="eye-sensitivity" min="0.1" max="3" step="0.1" value="1">
                        </div>
                        <div class="control-group">
                            <label>ارتفاع الطيران</label>
                            <input type="range" id="flying-height" min="0" max="5" step="0.5" value="2">
                        </div>
                        <div class="control-group">
                            <label>الإضاءة</label>
                            <input type="range" id="lighting" min="0.1" max="2" step="0.1" value="1">
                        </div>
                    </div>
                    
                    <!-- مؤشرات الحالة -->
                    <div class="status-indicators">
                        <div class="status-item">
                            <span class="status-label">الطاقة:</span>
                            <span class="status-value" id="power-status">نشط</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">الحالة:</span>
                            <span class="status-value" id="robot-status">جاهز</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">FPS:</span>
                            <span class="status-value" id="fps-counter">60</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- منطقة الروبوت ثلاثي الأبعاد -->
            <div class="robot-container">
                <canvas id="robot-canvas"></canvas>
                
                <!-- تأثيرات إضافية -->
                <div class="robot-effects">
                    <div class="energy-field"></div>
                    <div class="hologram-base"></div>
                    <div class="ambient-lighting">
                        <div class="light-beam"></div>
                        <div class="light-beam"></div>
                        <div class="light-beam"></div>
                    </div>
                </div>
                
                <!-- معلومات الروبوت -->
                <div class="robot-info">
                    <div class="info-panel">
                        <h4>معلومات الروبوت</h4>
                        <div class="info-item">
                            <span>الموديل:</span>
                            <span>REBOT-3D-Advanced</span>
                        </div>
                        <div class="info-item">
                            <span>الإصدار:</span>
                            <span>v2.0.1</span>
                        </div>
                        <div class="info-item">
                            <span>المحرك:</span>
                            <span>Three.js WebGL</span>
                        </div>
                        <div class="info-item">
                            <span>الذكاء الاصطناعي:</span>
                            <span>متقدم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مؤشرات التفاعل -->
        <div class="interaction-hints">
            <div class="hint">
                <span class="hint-icon">🖱️</span>
                <span class="hint-text">حرك الماوس للتفاعل مع الروبوت</span>
            </div>
            <div class="hint">
                <span class="hint-icon">👆</span>
                <span class="hint-text">اضغط على الروبوت لتفعيله</span>
            </div>
            <div class="hint">
                <span class="hint-icon">⚙️</span>
                <span class="hint-text">استخدم لوحة التحكم لتخصيص الإعدادات</span>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="info-overlay" id="info-overlay">
        <div class="overlay-content">
            <h2>حول REBOT 3D</h2>
            <p>
                REBOT 3D هو روبوت تفاعلي ثلاثي الأبعاد مبني بأحدث تقنيات الويب.
                يستخدم Three.js لعرض الجرافيك ثلاثي الأبعاد و GSAP للأنيميشن المتقدم.
            </p>
            <h3>المميزات:</h3>
            <ul>
                <li>تفاعل متقدم مع حركة الماوس</li>
                <li>أنيميشن سلس وواقعي</li>
                <li>تأثيرات بصرية متقدمة</li>
                <li>لوحة تحكم شاملة</li>
                <li>تصميم متجاوب</li>
            </ul>
            <button class="btn btn-primary" onclick="closeInfoOverlay()">إغلاق</button>
        </div>
    </div>

    <!-- أزرار التحكم السريع -->
    <div class="quick-controls">
        <button class="quick-btn" id="info-btn" title="معلومات">ℹ️</button>
        <button class="quick-btn" id="fullscreen-btn" title="ملء الشاشة">⛶</button>
        <button class="quick-btn" id="screenshot-btn" title="لقطة شاشة">📷</button>
        <button class="quick-btn" id="settings-btn" title="الإعدادات">⚙️</button>
    </div>

    <!-- سكريبت الروبوت -->
    <script src="robot.js"></script>
    <script src="script.js"></script>
</body>
</html>
