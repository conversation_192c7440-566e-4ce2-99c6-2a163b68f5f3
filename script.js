// متغيرات عامة
let robot;
let progressBar;
let loadingText;

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // عرض شاشة التحميل
    showLoadingScreen();
    
    // تحديث شريط التقدم
    updateProgress(0);
    
    // محاكاة تحميل الموارد
    simulateLoading();
    
    // إعداد الأحداث
    setupEventListeners();
    
    // إنشاء الروبوت بعد تحميل المكتبات
    setTimeout(() => {
        createRobot();
    }, 1000);
}

/**
 * عرض شاشة التحميل
 */
function showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    progressBar = document.getElementById('progress-bar');
    loadingText = document.getElementById('loading-text');
    
    if (loadingScreen) {
        loadingScreen.style.display = 'flex';
    }
}

/**
 * تحديث شريط التقدم
 */
function updateProgress(percentage) {
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
    
    // تحديث نص التحميل
    const messages = [
        'جاري تحميل المكتبات...',
        'إنشاء البيئة ثلاثية الأبعاد...',
        'تحميل نماذج الروبوت...',
        'إعداد الإضاءة والمواد...',
        'تهيئة الأنيميشن...',
        'اكتمل التحميل!'
    ];
    
    const messageIndex = Math.floor(percentage / 20);
    if (loadingText && messages[messageIndex]) {
        loadingText.textContent = messages[messageIndex];
    }
}

/**
 * محاكاة عملية التحميل
 */
function simulateLoading() {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
        }
        updateProgress(progress);
    }, 200);
}

/**
 * إنشاء الروبوت
 */
function createRobot() {
    try {
        robot = new AdvancedRobot();
        console.log('🤖 تم إنشاء الروبوت بنجاح!');
        
        // إخفاء شاشة التحميل بعد فترة قصيرة
        setTimeout(() => {
            hideLoadingScreen();
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الروبوت:', error);
        showError('فشل في تحميل الروبوت. يرجى إعادة تحميل الصفحة.');
    }
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        gsap.to(loadingScreen, {
            opacity: 0,
            duration: 0.5,
            onComplete: () => {
                loadingScreen.style.display = 'none';
            }
        });
    }
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
        loadingText.textContent = message;
        loadingText.style.color = '#ff4444';
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // زر إغلاق المعلومات
    window.closeInfoOverlay = function() {
        const infoOverlay = document.getElementById('info-overlay');
        if (infoOverlay) {
            infoOverlay.style.display = 'none';
        }
    };
    
    // النقر خارج نافذة المعلومات لإغلاقها
    const infoOverlay = document.getElementById('info-overlay');
    if (infoOverlay) {
        infoOverlay.addEventListener('click', function(e) {
            if (e.target === infoOverlay) {
                closeInfoOverlay();
            }
        });
    }
    
    // مفاتيح الاختصار
    document.addEventListener('keydown', function(e) {
        switch(e.key) {
            case ' ': // مسطرة المسافة
                e.preventDefault();
                if (robot) {
                    robot.toggleRobot();
                }
                break;
            case 'r':
            case 'R':
                if (robot) {
                    robot.resetRobot();
                }
                break;
            case 'i':
            case 'I':
                if (robot) {
                    robot.showInfo();
                }
                break;
            case 'f':
            case 'F':
                if (robot) {
                    robot.toggleFullscreen();
                }
                break;
            case 's':
            case 'S':
                if (robot) {
                    robot.takeScreenshot();
                }
                break;
            case 'Escape':
                closeInfoOverlay();
                break;
        }
    });
    
    // تأثيرات الأزرار
    addButtonEffects();
    
    // تأثيرات التمرير
    addScrollEffects();
    
    // جسيمات تفاعلية
    createInteractiveParticles();
}

/**
 * إضافة تأثيرات الأزرار
 */
function addButtonEffects() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: buttonRipple 0.6s ease-out;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        });
        
        // تأثير التمرير
        button.addEventListener('mouseenter', function() {
            gsap.to(this, { scale: 1.05, duration: 0.3, ease: "power2.out" });
        });
        
        button.addEventListener('mouseleave', function() {
            gsap.to(this, { scale: 1, duration: 0.3, ease: "power2.out" });
        });
    });
}

/**
 * إضافة تأثيرات التمرير
 */
function addScrollEffects() {
    // تأثير المنظور عند التمرير
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * 0.5;
        
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.style.transform = `translateY(${parallax}px)`;
        }
    });
    
    // تأثير الظهور عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر
    const animatedElements = document.querySelectorAll('.control-panel, .robot-info, .interaction-hints');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * إنشاء جسيمات تفاعلية
 */
function createInteractiveParticles() {
    const heroContainer = document.querySelector('.hero-container');
    if (!heroContainer) return;
    
    const particleContainer = document.createElement('div');
    particleContainer.className = 'interactive-particles-container';
    particleContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
    `;
    
    heroContainer.appendChild(particleContainer);
    
    // إنشاء جسيمات عند حركة الماوس
    heroContainer.addEventListener('mousemove', function(e) {
        if (Math.random() > 0.95) { // إنشاء جسيم بنسبة 5%
            createParticle(e.clientX, e.clientY, particleContainer);
        }
    });
}

/**
 * إنشاء جسيم واحد
 */
function createParticle(x, y, container) {
    const particle = document.createElement('div');
    const colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00'];
    const color = colors[Math.floor(Math.random() * colors.length)];
    const size = Math.random() * 6 + 2;
    
    particle.style.cssText = `
        position: absolute;
        left: ${x}px;
        top: ${y}px;
        width: ${size}px;
        height: ${size}px;
        background: ${color};
        border-radius: 50%;
        box-shadow: 0 0 ${size * 2}px ${color};
        pointer-events: none;
        animation: particleFloat 3s ease-out forwards;
    `;
    
    container.appendChild(particle);
    
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 3000);
}

/**
 * إضافة أنماط CSS للتأثيرات
 */
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes buttonRipple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        @keyframes particleFloat {
            0% {
                opacity: 1;
                transform: translateY(0px) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(0);
            }
        }
        
        .animate-in {
            animation: slideInUp 0.8s ease-out forwards;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* تحسينات الأداء */
        .robot-container,
        .hero-content,
        .control-panel {
            will-change: transform;
        }
        
        /* تأثيرات التمرير */
        .btn:hover {
            box-shadow: 0 15px 40px rgba(0, 255, 255, 0.4);
        }
        
        .quick-btn:hover {
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }
        
        /* تحسين الخطوط */
        .hero-title,
        .control-panel h3,
        .info-panel h4 {
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    `;
    document.head.appendChild(style);
}

// تهيئة الأنماط
addAnimationStyles();

// رسائل وحدة التحكم
console.log('🚀 REBOT 3D - روبوت ثلاثي الأبعاد متقدم');
console.log('💡 اختصارات لوحة المفاتيح:');
console.log('   • مسطرة المسافة: تفعيل/إيقاف الروبوت');
console.log('   • R: إعادة تعيين');
console.log('   • I: معلومات');
console.log('   • F: ملء الشاشة');
console.log('   • S: لقطة شاشة');
console.log('   • Escape: إغلاق النوافذ');

// معلومات الأداء
if (window.performance) {
    window.addEventListener('load', function() {
        const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
        console.log(`⚡ وقت التحميل: ${loadTime}ms`);
    });
}
