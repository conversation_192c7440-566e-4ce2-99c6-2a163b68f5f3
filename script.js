// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على عناصر الروبوت المتقدم
    const robot = document.getElementById('robot');
    const powerCore = document.querySelector('.power-core');
    const eyeLenses = document.querySelectorAll('.eye-lens');
    const eyeIrises = document.querySelectorAll('.eye-iris');
    const sensors = document.querySelectorAll('.sensor');
    const vents = document.querySelectorAll('.vent');
    const heroContainer = document.querySelector('.hero-container');

    // متغيرات للتحكم في الحالة
    let isRobotActive = true;
    let mouseX = 0;
    let mouseY = 0;
    let isMouseMoving = false;
    let mouseTimeout;

    // تفعيل/إلغاء تفعيل الروبوت
    function toggleRobot() {
        isRobotActive = !isRobotActive;

        if (isRobotActive) {
            robot.style.animationPlayState = 'running';

            // تشغيل نواة الطاقة
            if (powerCore) {
                powerCore.style.animationPlayState = 'running';
                powerCore.style.opacity = '1';
            }

            // تشغيل الحساسات
            sensors.forEach((sensor, index) => {
                setTimeout(() => {
                    sensor.style.animationPlayState = 'running';
                }, index * 300);
            });

            // تشغيل فتحات التهوية
            vents.forEach((vent, index) => {
                setTimeout(() => {
                    vent.style.animationPlayState = 'running';
                }, index * 200);
            });

            // تشغيل العيون
            eyeLenses.forEach(lens => {
                lens.style.animationPlayState = 'running';
            });

            // إضافة تأثير صوتي (محاكاة)
            createSoundEffect('activation');

        } else {
            robot.style.animationPlayState = 'paused';

            // إيقاف نواة الطاقة
            if (powerCore) {
                powerCore.style.animationPlayState = 'paused';
                powerCore.style.opacity = '0.3';
            }

            // إيقاف الحساسات
            sensors.forEach(sensor => {
                sensor.style.animationPlayState = 'paused';
            });

            // إيقاف فتحات التهوية
            vents.forEach(vent => {
                vent.style.animationPlayState = 'paused';
            });

            // إيقاف العيون
            eyeLenses.forEach(lens => {
                lens.style.animationPlayState = 'paused';
            });

            // إضافة تأثير صوتي (محاكاة)
            createSoundEffect('deactivation');
        }
    }

    // تتبع حركة الماوس
    function handleMouseMove(event) {
        if (!isRobotActive) return;

        const rect = heroContainer.getBoundingClientRect();
        mouseX = ((event.clientX - rect.left) / rect.width - 0.5) * 2;
        mouseY = ((event.clientY - rect.top) / rect.height - 0.5) * 2;

        isMouseMoving = true;

        // تحريك عيون الروبوت لتتبع الماوس
        moveEyes(mouseX, mouseY);

        // تدوير الروبوت قليلاً باتجاه الماوس
        rotateRobot(mouseX, mouseY);

        // إعادة تعيين مؤقت الحركة
        clearTimeout(mouseTimeout);
        mouseTimeout = setTimeout(() => {
            isMouseMoving = false;
            resetRobotPosition();
        }, 2000);
    }

    // تحريك العيون المتقدمة
    function moveEyes(x, y) {
        eyeIrises.forEach(iris => {
            const maxMove = 4; // أقصى حركة بالبكسل
            const moveX = x * maxMove;
            const moveY = y * maxMove;

            iris.style.transform = `translate(calc(-50% + ${moveX}px), calc(-50% + ${moveY}px))`;
        });

        // تأثير إضافي على العدسات
        eyeLenses.forEach((lens, index) => {
            const intensity = Math.abs(x) + Math.abs(y);
            const glowIntensity = 0.6 + (intensity * 0.4);

            setTimeout(() => {
                lens.style.boxShadow = `
                    0 0 ${15 + intensity * 10}px rgba(0, 255, 255, ${glowIntensity}),
                    inset 0 0 ${10 + intensity * 5}px rgba(255, 255, 255, 0.2)
                `;
            }, index * 50);
        });
    }

    // تدوير الروبوت
    function rotateRobot(x, y) {
        const maxRotation = 10; // أقصى دوران بالدرجات
        const rotateY = x * maxRotation;
        const rotateX = -y * maxRotation * 0.5;

        robot.style.transform = `rotateY(${rotateY}deg) rotateX(${rotateX}deg)`;
    }

    // إعادة تعيين موضع الروبوت
    function resetRobotPosition() {
        robot.style.transform = 'rotateY(0deg) rotateX(0deg)';

        eyeIrises.forEach(iris => {
            iris.style.transform = 'translate(-50%, -50%)';
        });

        // إعادة تعيين توهج العيون
        eyeLenses.forEach(lens => {
            lens.style.boxShadow = `
                0 0 15px rgba(0, 255, 255, 0.6),
                inset 0 0 10px rgba(255, 255, 255, 0.2)
            `;
        });
    }

    // إنشاء تأثير صوتي مرئي
    function createSoundEffect(type) {
        const soundWave = document.createElement('div');
        soundWave.className = 'sound-wave';

        if (type === 'activation') {
            soundWave.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                border: 2px solid #00ff00;
                border-radius: 50%;
                animation: soundWaveExpand 1s ease-out forwards;
                pointer-events: none;
                z-index: 1000;
            `;
        } else {
            soundWave.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                border: 2px solid #ff0000;
                border-radius: 50%;
                animation: soundWaveContract 1s ease-out forwards;
                pointer-events: none;
                z-index: 1000;
            `;
        }

        robot.appendChild(soundWave);

        // إزالة التأثير بعد انتهاء الأنيميشن
        setTimeout(() => {
            if (soundWave.parentNode) {
                soundWave.parentNode.removeChild(soundWave);
            }
        }, 1000);
    }

    // إضافة أنيميشن موجات الصوت
    const style = document.createElement('style');
    style.textContent = `
        @keyframes soundWaveExpand {
            0% {
                width: 50px;
                height: 50px;
                opacity: 1;
            }
            100% {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }

        @keyframes soundWaveContract {
            0% {
                width: 200px;
                height: 200px;
                opacity: 1;
            }
            100% {
                width: 50px;
                height: 50px;
                opacity: 0;
            }
        }

        .robot:hover {
            transform: scale(1.05) !important;
            transition: transform 0.3s ease;
        }

        .robot:hover .eye-glow {
            animation-duration: 1s !important;
        }

        .robot:hover .antenna-tip {
            animation-duration: 0.8s !important;
        }
    `;
    document.head.appendChild(style);

    // تأثير النقر على الأزرار
    function addButtonEffects() {
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // إنشاء تأثير الموجة
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });
        });
    }

    // إضافة أنيميشن الموجة للأزرار
    const buttonStyle = document.createElement('style');
    buttonStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(buttonStyle);

    // تأثير التمرير
    function addScrollEffects() {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = scrolled * 0.5;

            if (robot) {
                robot.style.transform = `translateY(${parallax}px) rotateY(${scrolled * 0.1}deg)`;
            }
        });
    }

    // تأثير الجسيمات التفاعلية
    function createInteractiveParticles() {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'interactive-particles';
        particleContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        `;

        heroContainer.appendChild(particleContainer);

        // إنشاء جسيمات عند حركة الماوس
        heroContainer.addEventListener('mousemove', function(e) {
            if (Math.random() > 0.9) { // إنشاء جسيم بنسبة 10%
                createParticle(e.clientX, e.clientY, particleContainer);
            }
        });
    }

    // إنشاء جسيم واحد
    function createParticle(x, y, container) {
        const particle = document.createElement('div');
        const colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00'];
        const color = colors[Math.floor(Math.random() * colors.length)];

        particle.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 4px;
            height: 4px;
            background: ${color};
            border-radius: 50%;
            box-shadow: 0 0 10px ${color};
            animation: particleFade 2s ease-out forwards;
            pointer-events: none;
        `;

        container.appendChild(particle);

        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 2000);
    }

    // إضافة أنيميشن الجسيمات والتأثيرات المتقدمة
    const particleStyle = document.createElement('style');
    particleStyle.textContent = `
        @keyframes particleFade {
            0% {
                opacity: 1;
                transform: translateY(0px) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-50px) scale(0);
            }
        }

        @keyframes jointPulse {
            0%, 100% {
                box-shadow:
                    0 8px 25px rgba(0, 0, 0, 0.3),
                    inset 0 3px 12px rgba(255, 255, 255, 0.4),
                    inset 0 -3px 12px rgba(0, 0, 0, 0.3);
            }
            50% {
                box-shadow:
                    0 8px 25px rgba(0, 0, 0, 0.3),
                    inset 0 3px 12px rgba(255, 255, 255, 0.4),
                    inset 0 -3px 12px rgba(0, 0, 0, 0.3),
                    0 0 15px rgba(0, 255, 255, 0.4);
            }
        }

        .robot:hover {
            transform: scale(1.02) !important;
            transition: transform 0.5s ease;
        }

        .robot:hover .eye-lens {
            animation-duration: 2s !important;
        }

        .robot:hover .core-center {
            animation-duration: 1s !important;
        }

        .robot:hover .energy-field {
            animation-duration: 8s !important;
        }
    `;
    document.head.appendChild(particleStyle);

    // تهيئة جميع التأثيرات
    function initializeEffects() {
        // إضافة مستمعي الأحداث
        if (powerCore) {
            powerCore.addEventListener('click', toggleRobot);
        }
        heroContainer.addEventListener('mousemove', handleMouseMove);

        // تهيئة التأثيرات
        addButtonEffects();
        addScrollEffects();
        createInteractiveParticles();
        addAdvancedRobotEffects();

        // تشغيل أنيميشن ترحيبي
        setTimeout(() => {
            createSoundEffect('activation');
        }, 1000);
    }

    // تأثيرات متقدمة للروبوت
    function addAdvancedRobotEffects() {
        // تأثير النقر على نواة الطاقة
        if (powerCore) {
            powerCore.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
                this.style.transition = 'transform 0.3s ease';
            });

            powerCore.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        }

        // تأثير التمرير على أجزاء الروبوت
        const robotParts = document.querySelectorAll('.chest-plate-main, .head-front, .arm-shell, .thigh-armor');
        robotParts.forEach(part => {
            part.addEventListener('mouseenter', function() {
                this.style.boxShadow = this.style.boxShadow + ', 0 0 20px rgba(0, 255, 255, 0.3)';
                this.style.transition = 'box-shadow 0.3s ease';
            });

            part.addEventListener('mouseleave', function() {
                this.style.boxShadow = this.style.boxShadow.replace(', 0 0 20px rgba(0, 255, 255, 0.3)', '');
            });
        });

        // تأثير النبض على المفاصل
        const joints = document.querySelectorAll('.shoulder-joint, .elbow-joint, .knee-joint, .ankle-joint');
        joints.forEach((joint, index) => {
            setTimeout(() => {
                joint.style.animation = joint.style.animation + ', jointPulse 4s ease-in-out infinite';
            }, index * 500);
        });
    }

    // بدء التطبيق
    initializeEffects();

    // إضافة رسالة ترحيبية في وحدة التحكم
    console.log('🤖 REBOT - مرحباً بك في عالم الروبوتات ثلاثية الأبعاد!');
    console.log('💡 نصائح التفاعل:');
    console.log('   • حرك الماوس لتتبع عيون الروبوت');
    console.log('   • اضغط على زر الطاقة لتشغيل/إيقاف الروبوت');
    console.log('   • مرر فوق الروبوت لتأثيرات إضافية');
});
